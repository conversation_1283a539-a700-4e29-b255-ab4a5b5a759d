/**
 * Performance optimization utilities for the website
 */

/**
 * Preload critical resources
 * This function can be called in useEffect to preload resources that will be needed soon
 */
export function preloadResources() {
  if (typeof window === 'undefined') return;

  // Preload critical images - updated with actual image paths
  const imagesToPreload = [
    '/images/facial-treatment-hero.jpg',
    '/images/spa-treatment-room.jpg',
    '/images/lash-lift-service.jpg',
    '/images/services/facials/sandy-facial.jpg',
    '/images/services/general/treatment-room.jpg',
  ];

  imagesToPreload.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    // Add format hints for better optimization
    if (src.includes('.jpg')) {
      link.type = 'image/webp';
    }
    document.head.appendChild(link);
  });
}

/**
 * Defer non-critical resources
 * This helps improve initial page load by deferring non-critical resources
 */
export function deferNonCriticalResources() {
  if (typeof window === 'undefined') return;

  // Add event listener for when the page is fully loaded
  window.addEventListener('load', () => {
    // Wait a bit after page load to start loading non-critical resources
    setTimeout(() => {
      // Load non-critical scripts or styles here if needed
      console.log('Loading deferred resources');
    }, 1000);
  });
}

// Add type declaration for deviceMemory
declare global {
  interface Navigator {
    deviceMemory?: number;
  }
}

/**
 * Optimize animations for performance
 * This function can be used to reduce animation complexity on low-end devices
 */
export function optimizeAnimations() {
  if (typeof window === 'undefined') return;

  // Check if the device is likely to be low-end
  const isLowEndDevice = () => {
    // Simple heuristic - can be improved
    return (
      navigator.hardwareConcurrency <= 4 ||
      (typeof navigator.deviceMemory !== 'undefined' && navigator.deviceMemory <= 4) ||
      window.matchMedia('(prefers-reduced-motion: reduce)').matches
    );
  };

  // Return settings based on device capability
  return {
    reducedMotion: isLowEndDevice(),
    // Other performance settings can be added here
  };
}

/**
 * Intersection Observer for lazy loading images
 * This provides better performance than the default lazy loading
 */
export function createImageObserver(callback: (entry: IntersectionObserverEntry) => void) {
  if (typeof window === 'undefined') return null;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(callback);
    },
    {
      rootMargin: '50px 0px', // Start loading 50px before the image enters viewport
      threshold: 0.1,
    }
  );

  return observer;
}

/**
 * Preload images when they're about to be needed
 * @param imagePaths Array of image paths to preload
 * @param delay Delay in milliseconds before preloading
 */
export function preloadImagesWithDelay(imagePaths: string[], delay: number = 1000) {
  if (typeof window === 'undefined') return;

  setTimeout(() => {
    imagePaths.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, delay);
}

/**
 * Monitor Core Web Vitals and performance metrics
 */
export function monitorPerformance() {
  if (typeof window === 'undefined') return;

  // Monitor Largest Contentful Paint (LCP)
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
        }
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  }

  // Monitor First Input Delay (FID)
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
        }
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
  }
}

/**
 * Optimize images based on connection speed
 */
export function getOptimalImageQuality(): number {
  if (typeof navigator === 'undefined') return 85;

  // Check connection speed if available
  const connection = (navigator as any).connection;
  if (connection) {
    const effectiveType = connection.effectiveType;

    switch (effectiveType) {
      case 'slow-2g':
      case '2g':
        return 60;
      case '3g':
        return 70;
      case '4g':
      default:
        return 85;
    }
  }

  return 85;
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
