/**
 * Image optimization utilities
 */

/**
 * Generate responsive image sizes based on the viewport and use case
 * @param baseSize Base size of the image in pixels
 * @param useCase Type of image usage (carousel, card, hero, etc.)
 * @returns A sizes string for the Next.js Image component
 */
export function generateResponsiveSizes(baseSize: number, useCase: 'carousel' | 'card' | 'hero' | 'thumbnail' | 'full' = 'card'): string {
  switch (useCase) {
    case 'carousel':
      return `(max-width: 640px) 280px, (max-width: 1024px) 320px, 320px`;
    case 'card':
      return `(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw`;
    case 'hero':
      return `(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 1920px`;
    case 'thumbnail':
      return `(max-width: 640px) 80px, (max-width: 768px) 96px, 128px`;
    case 'full':
      return `100vw`;
    default:
      return `(max-width: 640px) ${baseSize}px, (max-width: 768px) ${baseSize * 1.25}px, (max-width: 1024px) ${baseSize * 1.5}px, ${baseSize * 2}px`;
  }
}

/**
 * Determine if an image should be loaded with priority
 * @param index Index of the image in a list
 * @param isAboveFold Whether the image is above the fold
 * @param isHero Whether this is a hero image
 * @returns Boolean indicating if the image should be loaded with priority
 */
export function shouldPrioritizeImage(index: number, isAboveFold: boolean = false, isHero: boolean = false): boolean {
  // Prioritize hero images, first 2 images, or any above-fold images
  return isHero || index < 2 || isAboveFold;
}

/**
 * Get optimal image quality based on importance and device
 * @param isPriority Whether the image is a priority image
 * @param isRetina Whether the device is retina/high-DPI
 * @returns Quality value (1-100)
 */
export function getImageQuality(isPriority: boolean, isRetina: boolean = false): number {
  if (isPriority) {
    return isRetina ? 90 : 85;
  }
  return isRetina ? 80 : 75;
}

/**
 * Generate blur data URL for image placeholders with proper colors
 * @param color Hex color without # (default: website theme color)
 * @param opacity Opacity of the placeholder (0-1)
 * @returns Base64 encoded blur data URL
 */
export function getBlurDataUrl(color: string = 'F8F7F4', opacity: number = 0.3): string {
  const svg = `
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'>
      <rect width='1' height='1' fill='#${color}' opacity='${opacity}'/>
    </svg>
  `;
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
}

/**
 * Generate a gradient blur placeholder for better visual experience
 * @param colors Array of hex colors for gradient
 * @returns Base64 encoded gradient blur data URL
 */
export function getGradientBlurDataUrl(colors: string[] = ['F8F7F4', 'E8E5E1']): string {
  const svg = `
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'>
      <defs>
        <linearGradient id='grad' x1='0%' y1='0%' x2='100%' y2='100%'>
          <stop offset='0%' style='stop-color:#${colors[0]};stop-opacity:0.3' />
          <stop offset='100%' style='stop-color:#${colors[1] || colors[0]};stop-opacity:0.1' />
        </linearGradient>
      </defs>
      <rect width='1' height='1' fill='url(#grad)'/>
    </svg>
  `;
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
}

/**
 * Check if the browser supports modern image formats
 * @returns Object indicating support for WebP and AVIF
 */
export function checkImageFormatSupport(): { webp: boolean; avif: boolean } {
  if (typeof window === 'undefined') {
    return { webp: false, avif: false };
  }

  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;

  return {
    webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0,
    avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0,
  };
}

/**
 * Preload critical images with proper format detection
 * @param imagePaths Array of image paths to preload
 * @param priority Whether these are high priority images
 */
export function preloadImages(imagePaths: string[], priority: boolean = true): void {
  if (typeof window === 'undefined') return;

  const formatSupport = checkImageFormatSupport();

  imagePaths.forEach((path) => {
    const link = document.createElement('link');
    link.rel = priority ? 'preload' : 'prefetch';
    link.as = 'image';
    link.href = path;

    // Add format hints for better optimization
    if (formatSupport.avif && path.includes('.jpg') || path.includes('.png')) {
      link.type = 'image/avif';
    } else if (formatSupport.webp && path.includes('.jpg') || path.includes('.png')) {
      link.type = 'image/webp';
    }

    document.head.appendChild(link);
  });
}

/**
 * Get optimal loading strategy based on image position and importance
 * @param index Image index in a list
 * @param isVisible Whether the image is currently visible
 * @param isAboveFold Whether the image is above the fold
 * @returns Loading strategy object
 */
export function getLoadingStrategy(index: number, isVisible: boolean = false, isAboveFold: boolean = false) {
  const isPriority = shouldPrioritizeImage(index, isAboveFold);

  return {
    priority: isPriority,
    loading: isPriority ? 'eager' as const : 'lazy' as const,
    quality: getImageQuality(isPriority),
    placeholder: 'blur' as const,
    blurDataURL: getGradientBlurDataUrl(),
  };
}
