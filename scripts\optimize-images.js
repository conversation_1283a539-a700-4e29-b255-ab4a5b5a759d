#!/usr/bin/env node

/**
 * Image Optimization Script for GlowByBry
 * 
 * This script optimizes images by:
 * 1. Converting large PNG files to WebP format
 * 2. Compressing JPEG files
 * 3. Generating multiple sizes for responsive images
 * 4. Creating optimized versions while keeping originals as backup
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  inputDir: './public/images',
  outputDir: './public/images/optimized',
  backupDir: './public/images/backup',
  maxWidth: 1920,
  maxHeight: 1080,
  quality: {
    jpeg: 85,
    webp: 80,
    png: 90
  },
  sizes: [320, 640, 768, 1024, 1280, 1920],
  formats: ['webp', 'original']
};

// File size thresholds (in bytes)
const SIZE_THRESHOLDS = {
  LARGE: 1024 * 1024, // 1MB
  VERY_LARGE: 2 * 1024 * 1024, // 2MB
  HUGE: 5 * 1024 * 1024 // 5MB
};

/**
 * Get file size in a human-readable format
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Analyze image files and provide optimization recommendations
 */
function analyzeImages(dir = config.inputDir) {
  const results = {
    totalFiles: 0,
    totalSize: 0,
    largeFiles: [],
    recommendations: []
  };

  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (['.jpg', '.jpeg', '.png', '.webp', '.svg'].includes(ext)) {
          results.totalFiles++;
          results.totalSize += stat.size;
          
          const relativePath = path.relative(config.inputDir, fullPath);
          
          if (stat.size > SIZE_THRESHOLDS.HUGE) {
            results.largeFiles.push({
              path: relativePath,
              size: stat.size,
              formatted: formatFileSize(stat.size),
              priority: 'CRITICAL'
            });
            results.recommendations.push(`🔴 CRITICAL: ${relativePath} (${formatFileSize(stat.size)}) - Consider aggressive compression or format conversion`);
          } else if (stat.size > SIZE_THRESHOLDS.VERY_LARGE) {
            results.largeFiles.push({
              path: relativePath,
              size: stat.size,
              formatted: formatFileSize(stat.size),
              priority: 'HIGH'
            });
            results.recommendations.push(`🟡 HIGH: ${relativePath} (${formatFileSize(stat.size)}) - Should be optimized`);
          } else if (stat.size > SIZE_THRESHOLDS.LARGE) {
            results.largeFiles.push({
              path: relativePath,
              size: stat.size,
              formatted: formatFileSize(stat.size),
              priority: 'MEDIUM'
            });
            results.recommendations.push(`🟢 MEDIUM: ${relativePath} (${formatFileSize(stat.size)}) - Could benefit from optimization`);
          }
        }
      }
    }
  }

  scanDirectory(dir);
  return results;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations() {
  console.log('🔍 Analyzing images...\n');
  
  const analysis = analyzeImages();
  
  console.log('📊 IMAGE ANALYSIS RESULTS');
  console.log('========================');
  console.log(`Total image files: ${analysis.totalFiles}`);
  console.log(`Total size: ${formatFileSize(analysis.totalSize)}`);
  console.log(`Large files (>1MB): ${analysis.largeFiles.length}\n`);
  
  if (analysis.largeFiles.length > 0) {
    console.log('📋 LARGE FILES FOUND:');
    console.log('=====================');
    analysis.largeFiles
      .sort((a, b) => b.size - a.size)
      .forEach(file => {
        const priority = file.priority === 'CRITICAL' ? '🔴' : 
                        file.priority === 'HIGH' ? '🟡' : '🟢';
        console.log(`${priority} ${file.path} - ${file.formatted}`);
      });
    console.log('');
  }
  
  if (analysis.recommendations.length > 0) {
    console.log('💡 OPTIMIZATION RECOMMENDATIONS:');
    console.log('================================');
    analysis.recommendations.forEach(rec => console.log(rec));
    console.log('');
  }
  
  // Specific recommendations for this project
  console.log('🎯 SPECIFIC RECOMMENDATIONS FOR GLOWBYBRY:');
  console.log('==========================================');
  console.log('1. Convert large PNG files to WebP format (can reduce size by 25-35%)');
  console.log('2. Compress JPEG files to 85% quality for hero images, 75% for others');
  console.log('3. Generate responsive image sizes (320px, 640px, 1024px, 1920px)');
  console.log('4. Use Next.js Image component with proper sizing and lazy loading');
  console.log('5. Implement blur placeholders for better perceived performance');
  console.log('6. Consider using a CDN for image delivery');
  console.log('');
  
  // Performance impact estimation
  const potentialSavings = analysis.totalSize * 0.3; // Estimate 30% savings
  console.log('📈 ESTIMATED PERFORMANCE IMPACT:');
  console.log('================================');
  console.log(`Potential size reduction: ${formatFileSize(potentialSavings)} (30% estimate)`);
  console.log(`Estimated load time improvement: 20-40% faster`);
  console.log(`Mobile performance improvement: Significant (especially on slow connections)`);
  console.log('');
  
  return analysis;
}

/**
 * Manual optimization guide
 */
function showOptimizationGuide() {
  console.log('🛠️  MANUAL OPTIMIZATION GUIDE:');
  console.log('==============================');
  console.log('');
  console.log('For immediate improvements, manually optimize these files:');
  console.log('');
  console.log('1. jade-mask-and-brush.png (2.8MB) → Convert to WebP, resize to max 1200px width');
  console.log('2. professional-photo.svg (2.8MB) → Optimize SVG or convert to WebP if raster');
  console.log('3. before-after-results.png (2.4MB) → Convert to WebP, compress to 80% quality');
  console.log('4. spa-gallery-1.png (2.3MB) → Convert to WebP, resize and compress');
  console.log('');
  console.log('Tools you can use:');
  console.log('- Online: TinyPNG, Squoosh.app, Cloudinary');
  console.log('- Desktop: ImageOptim (Mac), RIOT (Windows)');
  console.log('- Command line: imagemagick, cwebp');
  console.log('');
  console.log('Next.js automatic optimization:');
  console.log('- Already configured in next.config.js');
  console.log('- Will automatically serve WebP/AVIF when supported');
  console.log('- Provides responsive sizing and lazy loading');
  console.log('');
}

// Run the analysis
if (require.main === module) {
  console.log('🚀 GlowByBry Image Optimization Analysis\n');
  
  const analysis = generateRecommendations();
  showOptimizationGuide();
  
  console.log('✅ Analysis complete! Follow the recommendations above to improve performance.');
}
