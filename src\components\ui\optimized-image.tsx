"use client";

import Image, { ImageProps } from 'next/image';

interface OptimizedImageProps extends ImageProps {
  // Keep the interface simple - just extend ImageProps directly
}

/**
 * Simple image component that uses Next.js Image with standard behavior
 */
export function OptimizedImage({
  className,
  ...props
}: OptimizedImageProps) {
  return (
    <Image
      {...props}
      className={className}
    />
  );
}
