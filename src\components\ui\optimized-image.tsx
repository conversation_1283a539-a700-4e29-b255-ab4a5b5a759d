"use client";

import Image, { ImageProps } from 'next/image';
import { useState, useEffect } from 'react';
import { 
  generateResponsiveSizes, 
  getLoadingStrategy, 
  getOptimalImageQuality 
} from '@/lib/imageOptimization';

interface OptimizedImageProps extends Omit<ImageProps, 'sizes' | 'quality' | 'loading' | 'priority' | 'placeholder' | 'blurDataURL'> {
  useCase?: 'carousel' | 'card' | 'hero' | 'thumbnail' | 'full';
  index?: number;
  isAboveFold?: boolean;
  isHero?: boolean;
  adaptiveQuality?: boolean;
}

/**
 * OptimizedImage component with automatic performance optimizations
 * 
 * Features:
 * - Automatic responsive sizing based on use case
 * - Adaptive quality based on connection speed
 * - Smart loading strategy (priority/lazy)
 * - Built-in blur placeholders
 * - Performance monitoring
 */
export function OptimizedImage({
  useCase = 'card',
  index = 0,
  isAboveFold = false,
  isHero = false,
  adaptiveQuality = true,
  className,
  onLoad,
  onError,
  ...props
}: OptimizedImageProps) {
  const [loadTime, setLoadTime] = useState<number | null>(null);
  const [hasError, setHasError] = useState(false);

  // Get optimal loading strategy
  const loadingStrategy = getLoadingStrategy(index, false, isAboveFold || isHero);
  
  // Get adaptive quality if enabled
  const quality = adaptiveQuality ? getOptimalImageQuality() : loadingStrategy.quality;
  
  // Generate responsive sizes
  const sizes = generateResponsiveSizes(320, useCase);

  useEffect(() => {
    // Performance monitoring
    if (loadTime && process.env.NODE_ENV === 'development') {
      console.log(`Image loaded in ${loadTime}ms:`, props.src);
    }
  }, [loadTime, props.src]);

  const handleLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    const endTime = performance.now();
    setLoadTime(endTime);
    onLoad?.(event);
  };

  const handleError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setHasError(true);
    console.warn('Image failed to load:', props.src);
    onError?.(event);
  };

  return (
    <Image
      {...props}
      className={className}
      sizes={sizes}
      quality={quality}
      loading={loadingStrategy.loading}
      priority={loadingStrategy.priority}
      placeholder={loadingStrategy.placeholder}
      blurDataURL={loadingStrategy.blurDataURL}
      onLoad={handleLoad}
      onError={handleError}
    />
  );
}

/**
 * Preload images for better performance
 */
export function preloadImage(src: string, priority: boolean = false): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
    
    if (priority && typeof document !== 'undefined') {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    }
  });
}

/**
 * Batch preload multiple images
 */
export function preloadImages(srcs: string[], priority: boolean = false): Promise<void[]> {
  return Promise.all(srcs.map(src => preloadImage(src, priority)));
}
