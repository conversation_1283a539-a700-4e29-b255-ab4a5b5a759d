"use client"

import { memo, useEffect, useLayoutEffect, useState, useRef } from "react"
import {
  motion,
  useMotionValue,
  useTransform,
  MotionValue
} from "framer-motion"
import Image from "next/image"

export const useIsomorphicLayoutEffect =
  typeof window !== "undefined" ? useLayoutEffect : useEffect

type UseMediaQueryOptions = {
  defaultValue?: boolean
}

const IS_SERVER = typeof window === "undefined"

export function useMediaQuery(
  query: string,
  {
    defaultValue = false,
  }: UseMediaQueryOptions = {}
): boolean {
  // Helper function to check if media query matches
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getMatches = (query: string): boolean => {
    if (IS_SERVER) {
      return defaultValue
    }
    return window.matchMedia(query).matches
  }

  const [matches, setMatches] = useState<boolean>(defaultValue)

  useEffect(() => {
    if (IS_SERVER) return;

    const matchMedia = window.matchMedia(query);

    setMatches(matchMedia.matches);

    const handleChange = () => {
      setMatches(matchMedia.matches);
    }

    matchMedia.addEventListener("change", handleChange);

    return () => {
      matchMedia.removeEventListener("change", handleChange);
    }
  }, [query]);

  return matches
}

const duration = 0.15
const transition = { duration, ease: [0.32, 0.72, 0, 1], filter: "blur(4px)" }

// Custom gradient overlay for images
const gradientOverlay = "linear-gradient(to top, rgba(139, 93, 83, 0.15), rgba(250, 247, 246, 0.05) 70%)"

// Add shine animation keyframes
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = `
    @keyframes shine {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }
  `
  document.head.appendChild(styleSheet)
}

interface CarouselProps {
  cards: string[]
  isCarouselActive: boolean
  onInteractionStart?: () => void
  onInteractionEnd?: (momentum?: number) => void
  rotation: MotionValue<number>
}

const Carousel = memo(
  function Carousel({
    cards,
    isCarouselActive,
    onInteractionStart,
    onInteractionEnd,
    rotation,
  }: CarouselProps) {
    const isScreenSizeSm = useMediaQuery("(max-width: 640px)")
    const cylinderWidth = isScreenSizeSm ? 1320 : 2160
    const faceCount = cards.length
    const faceWidth = cylinderWidth / faceCount
    const radius = cylinderWidth / (2 * Math.PI)
    const transform = useTransform(
      rotation,
      (value) => `rotate3d(0, 1, 0, ${value}deg)`
    )

    return (
      <div
        className="flex h-full w-full items-center justify-center"
        style={{
          perspective: "1000px",
          transformStyle: "preserve-3d",
          willChange: "transform",
        }}
      >
        <motion.div
          className="relative flex h-full origin-center justify-center"
          style={{
            transform,
            width: cylinderWidth,
            transformStyle: "preserve-3d",
          }}
          initial={false}
        >
          {cards.map((imgUrl, i) => (
            <motion.div
              key={`key-${imgUrl}-${i}`}
              className="absolute flex h-full origin-center items-center justify-center p-2"
              style={{
                width: `${faceWidth}px`,
                transform: `rotateY(${
                  i * (360 / faceCount)
                }deg) translateZ(${radius}px)`,
              }}
            >
              <motion.div
                className="w-full h-full relative cursor-grab active:cursor-grabbing"
                style={{
                  maxWidth: isScreenSizeSm ? "280px" : "320px",
                  height: isScreenSizeSm ? "210px" : "240px",
                  willChange: "transform",
                }}
                drag={isCarouselActive ? "x" : false}
                dragMomentum={false}
                dragElastic={0.02}
                dragConstraints={{ left: 0, right: 0 }}
                onDragStart={(e) => {
                  e.stopPropagation();
                  if (onInteractionStart) onInteractionStart();
                }}
                onTouchStart={(e) => {
                  e.stopPropagation();
                  if (onInteractionStart) onInteractionStart();
                }}
                onDrag={(_, info) => {
                  if (isCarouselActive) {
                    const baseSpeed = isScreenSizeSm ? 0.003 : 0.0015;
                    const velocity = Math.abs(info.velocity.x);
                    const velocityFactor = Math.min(velocity / 1200, 1);
                    const sensitivity = baseSpeed + (velocityFactor * baseSpeed * 1.5);
                    const newRotation = rotation.get() + info.delta.x * sensitivity;
                    rotation.set(newRotation);
                  }
                }}
                onDragEnd={(_, info) => {
                  if (!isCarouselActive) return;
                  const velocity = info.velocity.x;
                  const momentum = Math.min(Math.abs(velocity), 800) * Math.sign(velocity) * 0.0003;
                  if (onInteractionEnd) {
                    onInteractionEnd(momentum);
                  }
                }}
              >
                <motion.div
                  className="w-full h-full overflow-hidden rounded-2xl relative"
                  style={{
                    boxShadow: "0 10px 30px -5px rgba(139, 93, 83, 0.15)",
                    backfaceVisibility: "hidden",
                    WebkitBackfaceVisibility: "hidden",
                    transform: "translateZ(0)",
                    WebkitTransform: "translateZ(0)"
                  }}
                >
                  <motion.div
                    className="pointer-events-none h-full w-full relative z-10"
                    initial={{ filter: isScreenSizeSm ? "blur(2px)" : "blur(4px)" }}
                    animate={{ filter: "blur(0px)" }}
                    transition={transition}
                  >
                    <Image
                      src={imgUrl}
                      alt={`Spa/Beauty image ${i + 1}`}
                      fill
                      sizes="(max-width: 640px) 280px, (max-width: 1024px) 320px, 320px"
                      className="object-cover"
                      priority={i < 2}
                      loading={i < 2 ? "eager" : "lazy"}
                      quality={isScreenSizeSm ? (i < 2 ? 80 : 70) : (i < 3 ? 85 : 75)}
                      placeholder="blur"
                      blurDataURL="data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23F8F7F4;stop-opacity:0.3' /%3E%3Cstop offset='100%25' style='stop-color:%23E8E5E1;stop-opacity:0.1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1' height='1' fill='url(%23grad)'/%3E%3C/svg%3E"
                    />
                    <div
                      className="absolute inset-0 opacity-40 transition-opacity duration-700"
                      style={{
                        background: gradientOverlay
                      }}
                    />
                  </motion.div>
                </motion.div>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    )
  }
)

// Removed unused variables

// Use local images for reliable loading
const beautySpaImageUrls = [
  "/images/facial-treatment-hero.jpg",
  "/images/spa-treatment-room.jpg",
  "/images/lash-lift-service.jpg",
  "/images/microneedling-service.jpg",
  "/images/chemical-peel-service.jpg",
  "/images/dermaplaning-service.jpg",
  "/images/before-after-results.png",
  "/images/spa-gallery-1.png"
];

function ThreeDPhotoCarousel() {
  const rotation = useMotionValue(0)
  const [isCarouselActive] = useState(true)
  const [isMounted, setIsMounted] = useState(false)
  const [userInteracting, setUserInteracting] = useState(false)
  const velocityRef = useRef(0)
  const isScreenSizeSm = useMediaQuery("(max-width: 640px)")

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Fallback to placeholder images if local images don't load
  const [cards, setCards] = useState(beautySpaImageUrls)

  useEffect(() => {
    // If local images aren't found, fall back to reliable placeholders
    const checkImages = async () => {
      const fallbackImages = [
        "https://placehold.co/400x300/FFF0DB/A18A7C/png?text=Facial",
        "https://placehold.co/400x300/FEFBF6/B08D57/png?text=Massage",
        "https://placehold.co/400x300/FFF8E1/D4AC0D/png?text=Skincare",
        "https://placehold.co/400x300/FDF2E9/AF601A/png?text=Spa",
        "https://placehold.co/400x300/F8F0E3/8D6E63/png?text=Beauty",
        "https://placehold.co/400x300/FEFBF6/B08D57/png?text=Relax",
        "https://placehold.co/400x300/FFF8E1/D4AC0D/png?text=Glow",
        "https://placehold.co/400x300/FDF2E9/AF601A/png?text=Treatment"
      ];

      try {
        const imgPromises = beautySpaImageUrls.map(url => {
          const fullUrl = url.startsWith('/') ? window.location.origin + url : url;
          return fetch(fullUrl, { method: 'HEAD' })
            .then(response => response.ok)
            .catch(() => false);
        });

        const results = await Promise.all(imgPromises);
        if (results.some(valid => !valid)) {
          console.log("Some images failed to load, using fallbacks");
          setCards(fallbackImages);
        }
      } catch (error) {
        console.error("Error checking images:", error);
        setCards(fallbackImages);
      }
    };

    checkImages();
  }, []);

  // Ultra-smooth constant auto-rotation
  useEffect(() => {
    if (!isMounted || userInteracting) return;

    let lastTime = Date.now();
    const baseRotationSpeed = 0.006; // Reduced base rotation speed
    let currentVelocity = velocityRef.current;

    const autoRotateInterval = setInterval(() => {
      if (!isMounted) return;

      const now = Date.now();
      const deltaTime = now - lastTime;
      lastTime = now;

      // Smoother velocity decay
      if (Math.abs(currentVelocity) > 0.0001) {
        // More gradual decay for smoother transition
        currentVelocity *= 0.98; // Slightly smoother decay
      } else {
        currentVelocity = 0;
      }

      const currentRotation = rotation.get();
      const rotationAmount = deltaTime * (baseRotationSpeed + currentVelocity);
      rotation.set(currentRotation + rotationAmount);
    }, 16);

    return () => clearInterval(autoRotateInterval);
  }, [isMounted, rotation, userInteracting]);

  const handleInteractionStart = () => {
    velocityRef.current = 0;
    setUserInteracting(true);
  };

  const handleInteractionEnd = (momentum = 0) => {
    velocityRef.current = momentum;
    setTimeout(() => {
      setUserInteracting(false);
    }, 100); // Slightly increased for smoother transition
  };

  return (
    <motion.div layout className="relative w-full">
      <div className="relative h-[500px] w-full overflow-hidden flex items-center justify-center">
        <div className="relative z-10 h-full" style={{ width: isScreenSizeSm ? "280px" : "320px" }}>
          <Carousel
            cards={cards}
            isCarouselActive={isCarouselActive}
            onInteractionStart={handleInteractionStart}
            onInteractionEnd={handleInteractionEnd}
            rotation={rotation}
          />
        </div>
      </div>
    </motion.div>
  )
}

export default ThreeDPhotoCarousel;
