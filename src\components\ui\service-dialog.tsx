"use client";

import * as React from "react";
import { Check, ChevronRight, Clock, DollarSign, Heart, ChevronLeft, ChevronRight as ChevronRightIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ServiceDetail {
  id: string;
  name: string;
  price: number | string;
  duration: string;
  description: string;
  benefits: string[];
  details: string;
  process: {
    title: string;
    description: string;
  }[];
  images: string[];
  popularChoice?: boolean;
}

interface ServiceDialogProps {
  service: ServiceDetail;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ServiceDialog({ service, open, onOpenChange }: ServiceDialogProps) {
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === service.images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? service.images.length - 1 : prevIndex - 1
    );
  };

  // Reset image index when dialog opens with a new service
  React.useEffect(() => {
    if (open) {
      setCurrentImageIndex(0);
    }
  }, [open, service.id]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 bg-[#f8f1ee] flex flex-col overflow-hidden">
        {/* Add DialogTitle for accessibility - can be visually identical to our h2 */}
        <DialogTitle className="sr-only">{service.name}</DialogTitle>

        {/* Header */}
        <div className="px-6 py-6 bg-white border-b border-[#8b5d53]/10">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-2xl font-serif font-light text-[#5d3f39]">{service.name}</h2>
          </div>
          <div className="flex items-center space-x-4 text-[#5d3f39]">
            <div className="flex items-center">
              <DollarSign className="w-4 h-4 mr-1 text-[#8b5d53]" />
              <span className="font-light">
                {typeof service.price === 'string' && service.price.includes('-')
                  ? `$${service.price}`
                  : `$${service.price}`}
              </span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1 text-[#8b5d53]" />
              <span className="font-light">{service.duration}</span>
            </div>
          </div>
        </div>

        {/* Scrollable Content - improved to fix overflow issues */}
        <ScrollArea className="flex-1 overflow-y-auto">
          <div className="px-6 py-6 space-y-6 pb-8">
            {/* Image Carousel */}
            <div className="relative w-full h-80 rounded-xl overflow-hidden group bg-stone-50">
              <Image
                src={service.images[currentImageIndex]}
                alt={`${service.name} - image ${currentImageIndex + 1}`}
                fill
                className="object-contain transition-all duration-300"
                priority
                quality={85}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 80vw, 60vw"
                placeholder="blur"
                blurDataURL="data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23F8F7F4;stop-opacity:0.3' /%3E%3Cstop offset='100%25' style='stop-color:%23E8E5E1;stop-opacity:0.1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1' height='1' fill='url(%23grad)'/%3E%3C/svg%3E"
              />

              {service.popularChoice && (
                <div className="absolute top-4 right-4 bg-[#8b5d53] text-white px-3 py-1 rounded-full text-xs font-medium flex items-center z-10">
                  <Heart className="w-3 h-3 mr-1" /> Popular Choice
                </div>
              )}

              {/* Navigation buttons */}
              {service.images.length > 1 && (
                <>
                  <button
                    onClick={(e) => { e.stopPropagation(); prevImage(); }}
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 text-[#5d3f39] rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-white"
                    aria-label="Previous image"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={(e) => { e.stopPropagation(); nextImage(); }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 text-[#5d3f39] rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-white"
                    aria-label="Next image"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* Image indicators */}
              {service.images.length > 1 && (
                <div className="absolute bottom-3 left-0 right-0 flex justify-center gap-1.5">
                  {service.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={(e) => { e.stopPropagation(); setCurrentImageIndex(index); }}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index === currentImageIndex
                          ? 'bg-white scale-125'
                          : 'bg-white/60 hover:bg-white/80'
                      }`}
                      aria-label={`Go to image ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Description */}
            <div>
              <p className="text-[#5d3f39] text-lg font-light font-cormorant">{service.description}</p>
            </div>

            {/* Benefits */}
            <div className="space-y-4">
              <h3 className="font-serif font-light text-xl text-[#5d3f39]">Benefits</h3>
              <ul className="grid gap-3">
                {service.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start text-[#5d3f39] bg-white/50 p-3 rounded-lg">
                    <Check className="w-5 h-5 mr-3 text-[#8b5d53] shrink-0 mt-0.5" />
                    <span className="font-light font-cormorant">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Details and Process */}
            <div className="space-y-4">
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="w-full bg-white">
                  <TabsTrigger
                    value="details"
                    className="flex-1 data-[state=active]:bg-[#8b5d53] data-[state=active]:text-white font-serif font-light"
                  >
                    Details
                  </TabsTrigger>
                  <TabsTrigger
                    value="process"
                    className="flex-1 data-[state=active]:bg-[#8b5d53] data-[state=active]:text-white font-serif font-light"
                  >
                    Process
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="details" className="mt-4 bg-white/50 p-4 rounded-lg">
                  <div className="text-[#5d3f39] font-light font-cormorant">
                    {service.details}
                  </div>
                </TabsContent>
                <TabsContent value="process" className="mt-4">
                  <div className="space-y-4">
                    {service.process.map((step, index) => (
                      <div key={index} className="bg-white/50 p-4 rounded-lg">
                        <h4 className="font-serif font-light text-[#5d3f39] flex items-center mb-2">
                          <span className="flex items-center justify-center w-6 h-6 rounded-full bg-[#8b5d53] text-white text-sm mr-3">
                            {index + 1}
                          </span>
                          {step.title}
                        </h4>
                        <p className="text-[#5d3f39]/80 pl-9 font-light font-cormorant">{step.description}</p>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Book Button - added bottom margin for better spacing */}
            <Button className="w-full bg-[#8b5d53] hover:bg-[#5d3f39] text-white mb-4 font-serif font-light" size="lg" asChild>
              <Link href={`/booking?service=${service.id}`}>
                Book Now <ChevronRight className="w-4 h-4 ml-1" />
              </Link>
            </Button>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}