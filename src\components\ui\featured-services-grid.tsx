"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface BadgeData {
  text: string;
  variant: "default" | "secondary" | "destructive" | "outline";
}

interface FeaturedService {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number | string;
  duration?: string;
  featured?: boolean;
  link: string;
  badge?: BadgeData;
}

// Note: ServiceCard component removed as it's no longer used

interface FeaturedServicesGridProps {
  title: string;
  description: string;
  services: FeaturedService[];
  className?: string;
  onServiceClick?: (serviceId: string) => void;
}

export function FeaturedServicesGrid({
  title,
  description,
  services,
  className,
  onServiceClick
}: FeaturedServicesGridProps) {
  return (
    <section className={cn("hidden md:block py-16 px-4", className)}>
      <div className="container mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-10"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-light tracking-wide mb-3 text-[#5d3f39] uppercase font-playfair">{title}</h2>
          <p className="text-lg text-[#5d3f39]/80 max-w-2xl mx-auto font-playfair italic">{description}</p>
          <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-4"></div>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group featured-service-card relative bg-white rounded-lg shadow-sm hover:shadow-lg border border-stone-200 overflow-hidden hover:-translate-y-1 transition-all duration-300 cursor-pointer"
            >
              <div className="relative h-44 sm:h-52 lg:h-56">
                <Image
                  src={service.image}
                  alt={service.name}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                  quality={index < 3 ? 85 : 75}
                  loading={index < 3 ? "eager" : "lazy"}
                  priority={index < 2}
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23F8F7F4;stop-opacity:0.3' /%3E%3Cstop offset='100%25' style='stop-color:%23E8E5E1;stop-opacity:0.1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1' height='1' fill='url(%23grad)'/%3E%3C/svg%3E"
                  className="object-cover transition-transform duration-500 group-hover:scale-110 group-hover:brightness-75 pointer-events-none"
                />
                {service.badge && (
                  <div className="absolute top-2 right-2 z-20">
                    <Badge
                      variant={service.badge.variant}
                      className={cn(
                        "text-sm",
                        service.badge.variant === "default" && "bg-[#8b5d53] text-white",
                        service.badge.variant === "secondary" && "bg-stone-700 text-white",
                        service.badge.variant === "outline" && "bg-white/80 text-[#8b5d53] border-[#8b5d53]"
                      )}
                    >
                      {service.badge.text === "Most Popular" ||
                       service.badge.text === "Best Value" ||
                       service.badge.text === "Featured" ? (
                        <><Sparkles className="w-3 h-3 mr-1" /> {service.badge.text}</>
                      ) : (
                        service.badge.text
                      )}
                    </Badge>
                  </div>
                )}

                {/* Overlay with View Details button - appears on hover */}
                <div className="service-overlay pointer-events-none">
                  {/* For facial and lash services, link directly to the detailed sections at the bottom */}
                  {service.id === "facial-1" ? (
                    <Link
                      href="#facial-details"
                      className="view-details-btn pointer-events-auto relative z-20 bg-white text-[#8b5d53] px-6 py-3 rounded-md font-medium flex items-center gap-2 shadow-lg hover:bg-[#8b5d53] hover:text-white transition-all duration-300"
                    >
                      View Details <ArrowRight className="h-4 w-4" />
                    </Link>
                  ) : service.id === "massage-1" ? (
                    <Link
                      href="#massage-details"
                      className="view-details-btn pointer-events-auto relative z-20 bg-white text-[#8b5d53] px-6 py-3 rounded-md font-medium flex items-center gap-2 shadow-lg hover:bg-[#8b5d53] hover:text-white transition-all duration-300"
                    >
                      View Details <ArrowRight className="h-4 w-4" />
                    </Link>
                  ) : (
                    /* For all other services, open a popup dialog */
                    <button
                      onClick={() => onServiceClick?.(service.id === "facial-2" ? "collagen-therapy-details" :
                                                     service.id === "massage-2" ? "waxing-details" :
                                                     service.id === "facial-3" ? "lacarte-details" :
                                                     service.id === "package-1" ? "package-details" :
                                                     service.id)}
                      className="view-details-btn pointer-events-auto relative z-20 bg-white text-[#8b5d53] px-6 py-3 rounded-md font-medium flex items-center gap-2 shadow-lg hover:bg-[#8b5d53] hover:text-white transition-all duration-300"
                    >
                      View Details <ArrowRight className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              <div className="p-4">
                <h3 className="text-lg font-light text-[#5d3f39] mb-2 font-playfair">{service.name}</h3>
                <p className="text-sm text-[#5d3f39]/80 mb-3 line-clamp-2 font-light font-playfair">{service.description}</p>
                <p className="text-[#5d3f39] font-light text-sm font-playfair">
                  Starting at ${typeof service.price === 'string' && service.price.includes('-')
                    ? service.price.split('-')[0]
                    : service.price}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default FeaturedServicesGrid;