"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { motion, LayoutGroup } from "framer-motion";

import { LazyLoadSection } from "@/components/ui/lazy-section";
// import { usePerformanceMonitor } from "@/hooks/usePerformanceMonitor";
import { BeamsBackground } from "@/components/ui/beams-background";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MoveRight,
  Sparkles as FacialIcon,
  Waves as MassageIcon,
  Zap as CollagenInductionIcon,
  Home as HomeIcon,
  List as ServicesIcon,
  Phone as ContactIcon,
  Star,
  ArrowRight,
  Clock
} from "lucide-react";
import { TextRotate } from "@/components/ui/text-rotate";
import { Floating } from "@/components/ui/parallax-floating";

import { ShimmerButton } from "@/components/ui/shimmer-button";

import dynamic from 'next/dynamic';
import { fetchReviewsFromUrls, GoogleReview } from "@/lib/fetchGoogleReviews";

// Dynamically import ThreeDPhotoCarousel with SSR disabled
const ThreeDPhotoCarousel = dynamic(
  () => import('@/components/ui/3d-carousel'),
  {
    ssr: false
  }
);

export default function HomePage() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [reviews, setReviews] = useState<GoogleReview[]>([]);
  const [reviewCount, setReviewCount] = useState(0);
  const [averageRating, setAverageRating] = useState(5);

  useEffect(() => {
    setIsMounted(true);

    // Fetch Google reviews
    const loadReviews = async () => {
      try {
        const googleReviews = await fetchReviewsFromUrls(['https://maps.app.goo.gl/nTJAkuuFjQ6C3QdM7']);
        setReviews(googleReviews.slice(0, 3)); // Get first 3 reviews for the avatars
        setReviewCount(googleReviews.length);

        // Calculate average rating
        const avgRating = googleReviews.reduce((acc, review) => acc + review.rating, 0) / googleReviews.length;
        setAverageRating(Math.round(avgRating * 10) / 10);
      } catch (error) {
        console.error('Error loading reviews:', error);
      }
    };

    loadReviews();

    // Clean up on unmount
    return () => {
      // Remove any event listeners if added
    };
  }, []);

  const rotatingTexts = ["Radiance", "Relaxation", "Rejuvenation", "Natural Glow"];

  const descriptionText = "Personalized Beauty. Radiance Reimagined. You, but Glowier.";

  if (!isMounted) {
    return null;
  }

  return (
    <>
      <BeamsBackground />
      <div className="relative z-0">
        <motion.section
          className="w-full min-h-[90vh] overflow-hidden flex items-center justify-center relative pt-20 sm:pt-24 md:pt-28"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Floating sensitivity={-0.5} className="absolute inset-0 w-full h-full z-0">
            {/* Floating elements removed */}
          </Floating>

          <div className="container mx-auto px-4 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-4 items-center min-h-[60vh]">
              {/* Text Content - Left Side */}
              <div className="flex flex-col justify-center items-center lg:items-start text-center lg:text-left order-2 lg:order-1 lg:pl-16 xl:pl-20 2xl:pl-24">
                <motion.h1
                  className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl display-heading whitespace-pre-wrap leading-tight flex flex-col items-center lg:items-start justify-center space-y-2 sm:space-y-3 md:space-y-4"
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.4, ease: "easeOut", delay: 0.4 }}
                >
                  <span className="text-[#5d3f39] relative font-light tracking-wide uppercase">
                    YOUR GLOW-UP STARTS HERE
                    {/* Title glow effect */}
                    <span className="absolute -inset-1 blur-md bg-gradient-to-r from-[#d4b2a7]/20 via-[#8b5d53]/10 to-[#d4b2a7]/20 z-[-1] opacity-70"></span>
                    <span className="absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-[#8b5d53]/30 to-transparent"></span>
                  </span>
                  <LayoutGroup>
                    <motion.span layout className="flex whitespace-pre items-center justify-center lg:justify-start relative">
                      <TextRotate
                        texts={rotatingTexts}
                        mainClassName="inline-block text-[#8b5d53] py-0 pb-1 md:pb-2 lg:pb-3 overflow-hidden pr-2 font-playfair italic"
                        staggerDuration={0.03}
                        staggerFrom="last"
                        rotationInterval={3500}
                        splitBy="words"
                        elementLevelClassName="font-playfair italic"
                        transition={{ type: "spring", damping: 25, stiffness: 300 }}
                      />
                      {/* Rotating text glow */}
                      <span className="absolute -inset-1 blur-md bg-gradient-to-r from-[#8b5d53]/10 via-[#d4b2a7]/10 to-[#8b5d53]/10 z-[-1] opacity-70"></span>
                    </motion.span>
                  </LayoutGroup>
                </motion.h1>

                <motion.p
                  className="mt-3 sm:mt-4 md:mt-6 text-lg text-[#5d3f39] max-w-2xl font-light tracking-wide uppercase font-serif"
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.4, ease: "easeOut", delay: 0.6 }}
                >
                  {descriptionText}
                </motion.p>

                <div className="flex flex-col sm:flex-row justify-center lg:justify-start space-y-3 sm:space-y-0 sm:space-x-4 items-center mt-4 sm:mt-6 md:mt-8">
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.4, ease: "easeOut", delay: 0.8 }}
                  >
                    <ShimmerButton
                      shimmerColor="rgba(212, 178, 167, 0.5)"
                      shimmerSize="0.1em"
                      borderRadius="100px"
                      background="rgba(139, 93, 83, 0.95)"
                      className="px-6 sm:px-8 py-4 border-[#8b5d53]/30 shadow-lg hover:shadow-[#d4b2a7]/30"
                      onClick={() => { window.location.href = '/booking'; }}
                    >
                      <span className="relative z-10 flex items-center text-white">
                        Book Now <Clock className="ml-2 h-5 w-5" />
                      </span>
                    </ShimmerButton>
                  </motion.div>
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.4, ease: "easeOut", delay: 0.9 }}
                  >
                    <Button
                      asChild
                      variant="outline"
                      className="relative backdrop-blur-sm bg-white/70 border-[#8b5d53]/30 text-[#5d3f39] hover:text-[#8b5d53] rounded-full px-6 sm:px-8 py-6 h-auto shadow-sm group overflow-hidden"
                    >
                      <Link href="/services" className="relative z-10">
                        <span className="relative z-10 flex items-center">
                          Explore The Glow Menu <MoveRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                        </span>
                        {/* Subtle inner glow */}
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-[#d4b2a7]/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 z-0"></span>
                        {/* Border glow effect */}
                        <span className="absolute inset-0 rounded-full border border-[#8b5d53]/0 group-hover:border-[#8b5d53]/30 transition-colors duration-300"></span>
                        {/* Subtle outer glow */}
                        <span className="absolute -inset-1 rounded-full bg-gradient-to-r from-[#8b5d53]/0 via-[#8b5d53]/10 to-[#8b5d53]/0 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-700 z-0"></span>
                      </Link>
                    </Button>
                  </motion.div>
                </div>
              </div>

              {/* Professional Image - Right Side */}
              <motion.div
                className="flex justify-center lg:justify-end items-center order-1 lg:order-2"
                animate={{ opacity: 1, x: 0 }}
                initial={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
              >
                <div className="relative group">
                  {/* Main image container with minimal blending effect */}
                  <div className="relative w-96 h-[480px] sm:w-[450px] sm:h-[550px] lg:w-[500px] lg:h-[600px] overflow-hidden">
                    <Image
                      src="/images/bryanna-professional.svg"
                      alt="Bryanna - Professional Esthetician"
                      fill
                      className="object-cover object-center"
                    />

                    {/* Very subtle edge blending only - much reduced */}
                    <div className="absolute inset-0 bg-gradient-to-r from-[#faf7f6] via-transparent to-transparent opacity-10"></div>
                    <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-[#faf7f6]/15"></div>

                    {/* Minimal soft edge blending */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-[#faf7f6] via-transparent to-transparent blur-xl opacity-15"></div>
                    <div className="absolute -inset-4 bg-gradient-to-l from-transparent via-transparent to-[#faf7f6]/20 blur-xl opacity-15"></div>
                  </div>

                  {/* Very subtle background blending - greatly reduced */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-[#faf7f6] via-transparent to-transparent opacity-10 blur-3xl pointer-events-none"></div>
                  <div className="absolute -inset-8 bg-gradient-to-l from-transparent via-transparent to-[#faf7f6]/15 opacity-10 blur-3xl pointer-events-none"></div>

                  {/* Minimal floating elements */}
                  <div className="absolute -top-6 -right-6 w-16 h-16 bg-gradient-to-br from-[#d4b2a7]/8 to-transparent rounded-full blur-xl animate-pulse opacity-30"></div>
                  <div className="absolute -bottom-8 -left-8 w-20 h-20 bg-gradient-to-tr from-[#8b5d53]/5 to-transparent rounded-full blur-2xl animate-pulse delay-1000 opacity-25"></div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>

        <LazyLoadSection className="relative py-16 pt-28 bg-gradient-to-b from-[#faf7f6]/0 via-[#f8f1ee]/30 to-[#f8f1ee]/40 overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute top-1/4 right-[5%] w-32 h-32 rounded-full bg-[#8b5d53]/10 blur-xl" />
          <div className="absolute bottom-1/3 left-[10%] w-40 h-40 rounded-full bg-[#d4b2a7]/15 blur-xl" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] rounded-full bg-[#f0e4df]/30 blur-3xl opacity-50 z-0" />

          {/* Diagonal decorative line */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none z-0">
            <div className="absolute -top-[10%] -left-[5%] w-[120%] h-[20%] bg-[#8b5d53]/5 rotate-[5deg] transform origin-top-left"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
              <motion.div
                className="md:col-span-5 text-left"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Badge className="mb-4 relative overflow-hidden group px-4 sm:px-5 py-1 sm:py-1.5 border-none shadow-sm rounded-lg sm:rounded-md transition-all duration-300 hover:shadow-md">
                  {/* Gradient background with animation */}
                  <span className="absolute inset-0 bg-gradient-to-r from-[#5d3f39] via-[#8b5d53] to-[#b07c70] opacity-100 group-hover:opacity-90 transition-opacity duration-300"></span>

                  {/* Subtle shine effect on hover */}
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-[length:200%_100%] animate-[shine_1.5s_ease-in-out_infinite]"></span>

                  {/* Text with enhanced styling */}
                  <span className="relative z-10 font-medium text-white flex items-center">
                    <span className="text-white/90 mr-0.5 text-sm">#</span>
                    <span className="tracking-wide">GlowGoals</span>
                  </span>
                </Badge>
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-light text-[#5d3f39] mb-4 tracking-wide uppercase font-serif">
                  Glow Goals?
                </h2>
                <div className="relative">
                  <p className="text-xl md:text-2xl lg:text-3xl font-light text-[#5d3f39] mb-6 tracking-wide uppercase font-serif">
                    Achieved.
                  </p>
                  <div className="h-1 w-20 bg-[#8b5d53]/30 rounded-full"></div>
                </div>
                <p className="mt-6 text-lg text-[#5d3f39] max-w-md mx-auto font-light tracking-wide font-serif">
                  We don&apos;t just do facials, we serve glow-ups. Think personalized, feel-good treatments that boost your natural radiance. Sending you off as the most radiant version of yourself, main character status unlocked.
                </p>
                <div className="mt-8 flex items-center space-x-4">
                  <div className="flex -space-x-2">
                    {/* Actual Google profile pictures for Jennifer Marquez, Jennifer Duff, and Laura Hale */}
                    {[
                      {
                        name: "Jennifer Marquez",
                        image: "https://lh3.googleusercontent.com/a-/ALV-UjV3SC3DK441vl3MPc9_FjkYa-OygcyXnTA06xUCnIZjpxX8LvVu=w90-h90-p-rp-mo-br100"
                      },
                      {
                        name: "Jennifer Duff",
                        image: "https://lh3.googleusercontent.com/a-/ALV-UjXVtVM2T00BbqTZU0ja-XarKkar24mYUAPrwtCI-cHLbBhDaWA=w90-h90-p-rp-mo-br100"
                      },
                      {
                        name: "Laura Hale",
                        image: "https://lh3.googleusercontent.com/a-/ALV-UjVvrK4bCMlhn7xKgKe6EEYxFANYkoWz-YUQVjELbj8nG-xjxkDL=w90-h90-p-rp-mo-ba4-br100"
                      }
                    ].map((reviewer, i) => (
                      <div key={i} className="w-10 h-10 rounded-full border-2 border-white overflow-hidden relative shadow-md">
                        <Image
                          src={reviewer.image}
                          alt={`Review by ${reviewer.name}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                  <Link
                    href="/services#google-reviews"
                    className="flex items-center group hover:opacity-80 transition-opacity duration-300"
                    onClick={(e) => {
                      e.preventDefault();
                      // Store scroll target for services page
                      sessionStorage.setItem('scrollToElement', 'google-reviews');
                      // Navigate to services page
                      router.push('/services');
                    }}
                  >
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${i <= averageRating ? 'text-amber-400 fill-amber-400' : 'text-gray-300 fill-gray-300'}`}
                        />
                      ))}
                    </div>
                    <div className="ml-2 relative">
                      <span className="text-sm text-[#5d3f39]/70">
                        {reviewCount > 0 ? '50+' : '100+'} reviews
                        <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#5d3f39]/0 group-hover:bg-[#5d3f39]/20 transition-colors duration-300"></span>
                      </span>
                      <ArrowRight className="w-3 h-3 inline-block ml-1 text-[#5d3f39]/70 transform translate-y-0 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </Link>
                </div>
              </motion.div>

              <motion.div
                className="md:col-span-7"
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="relative z-0 w-full rounded-2xl overflow-hidden shadow-lg">
                  {isMounted && <ThreeDPhotoCarousel />}
                </div>
              </motion.div>
            </div>
          </div>
        </LazyLoadSection>

        <LazyLoadSection className="relative pb-24 pt-16 overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-b from-[#f8f1ee]/80 via-white/90 to-[#f8f1ee]/70 z-0"></div>

          {/* Decorative elements */}
          <div className="absolute top-1/3 right-[10%] w-40 h-40 rounded-full bg-gradient-to-bl from-[#d4b2a7]/10 to-[#8b5d53]/5 blur-xl animate-pulse-slow" />
          <div className="absolute bottom-1/4 left-[5%] w-32 h-32 rounded-full bg-gradient-to-tr from-[#8b5d53]/10 to-[#d4b2a7]/10 blur-xl animate-pulse-slow-reverse" />
          <div className="absolute top-1/4 left-[15%] w-24 h-24 rounded-full bg-gradient-to-r from-[#e8d0c9]/10 to-transparent blur-xl animate-float" />
          <div className="absolute bottom-1/3 right-[15%] w-20 h-20 rounded-full bg-gradient-to-l from-[#e8d0c9]/10 to-transparent blur-xl animate-float-delay" />

          <div className="container mx-auto px-4 relative z-10">
            <motion.div
              className="mb-16 text-center relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              {/* Decorative elements for the title */}
              <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-40 h-40 bg-gradient-to-br from-[#d4b2a7]/10 to-transparent rounded-full blur-xl opacity-70"></div>

              <Badge className="mb-4 relative overflow-hidden group px-4 sm:px-5 py-1 sm:py-1.5 border-none shadow-sm rounded-lg sm:rounded-md transition-all duration-300 hover:shadow-md z-10">
                {/* Gradient background with animation */}
                <span className="absolute inset-0 bg-gradient-to-r from-[#5d3f39] via-[#8b5d53] to-[#b07c70] opacity-100 group-hover:opacity-90 transition-opacity duration-300"></span>

                {/* Subtle shine effect on hover */}
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-[length:200%_100%] animate-[shine_1.5s_ease-in-out_infinite]"></span>

                {/* Text with enhanced styling */}
                <span className="relative z-10 font-medium text-white tracking-wide">
                  Signature Services
                </span>
              </Badge>

              <h2 className="text-3xl md:text-5xl font-light text-[#5d3f39] mb-4 relative tracking-wide uppercase font-serif">
                <span className="relative inline-block">
                  It Girl Essentials
                  <span className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#8b5d53]/40 to-transparent"></span>
                </span>
              </h2>

              <p className="text-lg text-[#5d3f39] max-w-2xl mx-auto mb-8 font-light tracking-wide uppercase font-serif">
                Made to level up your natural glow
              </p>

              <div className="relative h-0.5 w-32 mx-auto">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#8b5d53]/50 to-transparent rounded-full"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent rounded-full opacity-50 animate-pulse-slow"></div>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* First Card - Customized Facials */}
              <motion.div
                className="relative bg-white/80 p-8 rounded-2xl text-center backdrop-blur-md border border-[#8b5d53]/10 shadow-md flex flex-col items-center transition-all duration-500 group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 15px 30px -10px rgba(139, 93, 83, 0.15)"
                }}
              >
                {/* Card background gradient */}
                <div className="absolute inset-0 bg-gradient-to-b from-white via-white to-[#f8f1ee]/50 opacity-80 z-0"></div>
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[#d4b2a7]/10 to-transparent rounded-bl-3xl z-0"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-[#d4b2a7]/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                {/* Icon */}
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-white to-[#f0e4df] flex items-center justify-center mb-8 shadow-md group-hover:shadow-lg group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-[#d4b2a7]/30 transition-all duration-500 z-1">
                  <FacialIcon className="w-8 h-8 text-[#8b5d53] relative z-1" />
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold mb-3 text-[#5d3f39] relative z-1">
                  Personalized Facials
                  <span className="block h-0.5 w-0 bg-gradient-to-r from-transparent via-[#8b5d53]/30 to-transparent group-hover:w-full transition-all duration-500 mt-1 mx-auto"></span>
                </h3>

                <p className="text-lg text-[#5d3f39] flex-grow mb-8 relative z-1 font-light tracking-wide font-serif">
                  Glow like you mean it. Crafted to celebrate your one-of-a-kind glow. Unlock your most radiant complexion yet.
                </p>

                {/* Button */}
                <Button
                  asChild
                  className="relative bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white rounded-full px-6 py-2 h-auto shadow-md transition-all duration-300"
                >
                  <Link href="/booking">
                    <span className="flex items-center">
                      Book Now <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  </Link>
                </Button>
              </motion.div>

              {/* Second Card - Lash Lift */}
              <motion.div
                className="relative bg-white/80 p-8 rounded-2xl text-center backdrop-blur-md border border-[#8b5d53]/10 shadow-md flex flex-col items-center transition-all duration-500 group z-20"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 15px 30px -10px rgba(139, 93, 83, 0.15)"
                }}
                style={{ isolation: 'isolate' }}
              >
                {/* Card background gradient */}
                <div className="absolute inset-0 bg-gradient-to-b from-white via-white to-[#f8f1ee]/50 opacity-80 z-0 pointer-events-none"></div>
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[#d4b2a7]/10 to-transparent rounded-bl-3xl z-0 pointer-events-none"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-[#d4b2a7]/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0 pointer-events-none"></div>

                {/* Icon */}
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-white to-[#f0e4df] flex items-center justify-center mb-8 shadow-md group-hover:shadow-lg group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-[#d4b2a7]/30 transition-all duration-500 z-1">
                  <MassageIcon className="w-8 h-8 text-[#8b5d53] relative z-1" />
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold mb-3 text-[#5d3f39] relative z-1">
                  Lash Lift
                  <span className="block h-0.5 w-0 bg-gradient-to-r from-transparent via-[#8b5d53]/30 to-transparent group-hover:w-full transition-all duration-500 mt-1 mx-auto"></span>
                </h3>

                <p className="text-lg text-[#5d3f39] flex-grow mb-8 relative z-1 font-light tracking-wide font-serif">
                  Enhance your natural lashes with a semi-permanent curl and lift that opens up your eyes and creates a wide-awake, mascara effect.
                </p>

                {/* Using standard link without any complex nesting */}
                <div className="relative z-50">
                  <a
                    href="/booking"
                    className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white px-6 py-2 text-sm font-medium shadow-md"
                    style={{
                      position: 'relative',
                      zIndex: 9999
                    }}
                  >
                    <span className="flex items-center">
                      Book Now <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  </a>
                </div>
              </motion.div>

              {/* Third Card - Microneedling */}
              <motion.div
                className="relative bg-white/80 p-8 rounded-2xl text-center backdrop-blur-md border border-[#8b5d53]/10 shadow-md flex flex-col items-center transition-all duration-500 group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 15px 30px -10px rgba(139, 93, 83, 0.15)"
                }}
              >
                {/* Card background gradient */}
                <div className="absolute inset-0 bg-gradient-to-b from-white via-white to-[#f8f1ee]/50 opacity-80 z-0"></div>
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[#d4b2a7]/10 to-transparent rounded-bl-3xl z-0"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-[#d4b2a7]/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                {/* Icon */}
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-white to-[#f0e4df] flex items-center justify-center mb-8 shadow-md group-hover:shadow-lg group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-[#d4b2a7]/30 transition-all duration-500 z-1">
                  <CollagenInductionIcon className="w-8 h-8 text-[#8b5d53] relative z-1" />
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold mb-3 text-[#5d3f39] relative z-1">
                  Collagen Induction
                  <span className="block h-0.5 w-0 bg-gradient-to-r from-transparent via-[#8b5d53]/30 to-transparent group-hover:w-full transition-all duration-500 mt-1 mx-auto"></span>
                </h3>

                <p className="text-lg text-[#5d3f39] flex-grow mb-8 relative z-1 font-light tracking-wide font-serif">
                  Stimulate collagen production for smoother, firmer, and younger-looking skin. Perfect for reducing fine lines and improving texture.
                </p>

                {/* Button */}
                <Button
                  asChild
                  className="relative bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white rounded-full px-6 py-2 h-auto shadow-md transition-all duration-300"
                >
                  <Link href="/booking">
                    <span className="flex items-center">
                      Book Now <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  </Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </LazyLoadSection>
      </div>
    </>
  );
}